import { parseSpecsDirectory, type ParsedSpec, type ParseError } from '@workflow-mapper/spec-parser-lib';
import { type CodeParseResult, type CallGraphResult } from '@workflow-mapper/code-parser-lib';
import { writeFileSync } from 'fs';
import { join } from 'path';
import { stringify as yamlStringify } from 'yaml';

export interface KnowledgeGraphOptions {
  dryRun?: boolean;
  outputDir?: string;
  codeParseResult?: CodeParseResult;
  callGraphResult?: CallGraphResult;
}

export interface KnowledgeGraphResult {
  summary: {
    specsCount: number;
    milestonesCount: number;
    componentsCount: number;
    relationshipsCount: number;
    functionsCount: number;
    workflowCallsCount: number;
  };
  files: {
    jsonld?: string;
    yaml?: string;
  };
  errors: ParseError[];
}

export interface GraphNode {
  '@id': string;
  '@type': string;
  title?: string;
  description?: string;
  status?: string;
  version?: string;
  created?: string;
  updated?: string;
  tags?: string[];
  authors?: string[];
  filePath: string;
  // Optional properties for function nodes
  name?: string;
  signature?: string;
  lang?: string;
  line_start?: number;
  line_end?: number;
}

export interface GraphRelationship {
  '@type': string;
  source: string;
  target: string;
  relationship: string;
  // Optional properties for workflow_calls
  call_type?: string;
  confidence?: number;
  file?: string;
  line?: number;
}

export interface KnowledgeGraph {
  '@context': {
    '@vocab': string;
    title: string;
    description: string;
    status: string;
    version: string;
    created: string;
    updated: string;
    tags: string;
    authors: string;
    filePath: string;
    implements: string;
    dependsOn: string;
    contains: string;
  };
  '@graph': (GraphNode | GraphRelationship)[];
}

/**
 * Build a knowledge graph from MDX specifications
 */
export async function buildKnowledgeGraph(
  directory: string,
  options: KnowledgeGraphOptions = {}
): Promise<KnowledgeGraphResult> {
  const { dryRun = false, outputDir = '.' } = options;

  // Parse all MDX files
  const parseResult = parseSpecsDirectory(directory);

  // Build the knowledge graph
  const graph = buildGraph(parseResult.specs, options.codeParseResult, options.callGraphResult);

  // Generate outputs
  const jsonldContent = JSON.stringify(graph, null, 2);
  const yamlContent = yamlStringify(graph, { indent: 2 });

  const files: { jsonld?: string; yaml?: string; } = {};

  if (!dryRun) {
    // Write files
    const jsonldPath = join(outputDir, 'kg.jsonld');
    const yamlPath = join(outputDir, 'kg.yaml');

    writeFileSync(jsonldPath, jsonldContent, 'utf-8');
    writeFileSync(yamlPath, yamlContent, 'utf-8');

    files.jsonld = jsonldPath;
    files.yaml = yamlPath;
  }

  // Calculate summary
  const nodes = graph['@graph'].filter(item => item['@type'] !== 'Relationship') as GraphNode[];
  const relationships = graph['@graph'].filter(item => item['@type'] === 'Relationship') as GraphRelationship[];

  const milestonesCount = nodes.filter(node =>
    node['@type'] === 'Milestone' ||
    (node.filePath && node.filePath.includes('/milestones/')) ||
    node.tags?.includes('milestone')
  ).length;

  const componentsCount = nodes.filter(node =>
    node['@type'] === 'Component' ||
    (node.filePath && node.filePath.includes('/components/')) ||
    node.tags?.includes('component')
  ).length;

  // Count code-related items
  const functionsCount = options.codeParseResult?.functions.length || 0;
  const workflowCallsCount = options.callGraphResult?.edges.length || 0;

  return {
    summary: {
      specsCount: parseResult.specs.length,
      milestonesCount,
      componentsCount,
      relationshipsCount: relationships.length,
      functionsCount,
      workflowCallsCount,
    },
    files,
    errors: parseResult.errors,
  };
}

/**
 * Build the knowledge graph structure from parsed specs and code
 */
function buildGraph(
  specs: ParsedSpec[],
  codeParseResult?: CodeParseResult,
  callGraphResult?: CallGraphResult
): KnowledgeGraph {
  const nodes: GraphNode[] = [];
  const relationships: GraphRelationship[] = [];

  // Create nodes for each spec
  for (const spec of specs) {
    const node: GraphNode = {
      '@id': spec.id,
      '@type': determineNodeType(spec),
      filePath: spec.filePath,
      ...spec.frontmatter,
    };

    nodes.push(node);

    // Extract relationships from content and frontmatter
    const specRelationships = extractRelationships(spec);
    relationships.push(...specRelationships);
  }

  // Add function nodes from code parsing
  if (codeParseResult) {
    for (const func of codeParseResult.functions) {
      const functionNode: GraphNode = {
        '@id': func.id,
        '@type': 'function',
        title: func.name,
        description: `Function ${func.signature} in ${func.file}`,
        filePath: func.file,
        name: func.name,
        signature: func.signature,
        lang: func.lang,
        line_start: func.line_start,
        line_end: func.line_end,
      };
      nodes.push(functionNode);
    }
  }

  // Add workflow call edges from call graph
  if (callGraphResult) {
    for (const edge of callGraphResult.edges) {
      const workflowEdge: GraphRelationship = {
        '@type': 'workflow_calls',
        source: edge.source,
        target: edge.target,
        relationship: 'calls',
        call_type: edge.call_type,
        confidence: edge.confidence,
        file: edge.file,
        line: edge.line,
      };
      relationships.push(workflowEdge);
    }
  }

  return {
    '@context': {
      '@vocab': 'https://workflow-mapper.dev/vocab#',
      title: 'https://schema.org/name',
      description: 'https://schema.org/description',
      status: 'https://workflow-mapper.dev/vocab#status',
      version: 'https://schema.org/version',
      created: 'https://schema.org/dateCreated',
      updated: 'https://schema.org/dateModified',
      tags: 'https://schema.org/keywords',
      authors: 'https://schema.org/author',
      filePath: 'https://workflow-mapper.dev/vocab#filePath',
      implements: 'https://workflow-mapper.dev/vocab#implements',
      dependsOn: 'https://workflow-mapper.dev/vocab#dependsOn',
      contains: 'https://workflow-mapper.dev/vocab#contains',
    },
    '@graph': [...nodes, ...relationships],
  };
}

/**
 * Determine the type of a node based on its spec
 */
function determineNodeType(spec: ParsedSpec): string {
  // Check file path patterns
  const filePath = spec.filePath || '';
  if (filePath.includes('/milestones/')) {
    return 'Milestone';
  }
  if (filePath.includes('/components/')) {
    return 'Component';
  }
  if (filePath.includes('/domains/')) {
    return 'Domain';
  }
  if (filePath.includes('/adrs/')) {
    return 'ArchitecturalDecision';
  }

  // Check frontmatter tags
  if (spec.frontmatter.tags?.includes('milestone')) {
    return 'Milestone';
  }
  if (spec.frontmatter.tags?.includes('component')) {
    return 'Component';
  }
  if (spec.frontmatter.tags?.includes('domain')) {
    return 'Domain';
  }
  if (spec.frontmatter.tags?.includes('adr')) {
    return 'ArchitecturalDecision';
  }

  // Default to Specification
  return 'Specification';
}

/**
 * Extract relationships from a spec
 */
function extractRelationships(spec: ParsedSpec): GraphRelationship[] {
  const relationships: GraphRelationship[] = [];

  // Extract from frontmatter
  if (spec.frontmatter.implements) {
    const implementsTargets = Array.isArray(spec.frontmatter.implements)
      ? spec.frontmatter.implements
      : [spec.frontmatter.implements];

    for (const target of implementsTargets) {
      relationships.push({
        '@type': 'Relationship',
        source: spec.id,
        target: String(target),
        relationship: 'implements',
      });
    }
  }

  if (spec.frontmatter.dependsOn) {
    const dependsOn = Array.isArray(spec.frontmatter.dependsOn)
      ? spec.frontmatter.dependsOn
      : [spec.frontmatter.dependsOn];

    for (const target of dependsOn) {
      relationships.push({
        '@type': 'Relationship',
        source: spec.id,
        target: String(target),
        relationship: 'dependsOn',
      });
    }
  }

  // TODO: Extract relationships from content (e.g., links to other specs)
  // This could parse markdown links and cross-references

  return relationships;
}

export { type ParsedSpec, type ParseError } from '@workflow-mapper/spec-parser-lib';
