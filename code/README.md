# Workflow Mapper

[![CI](https://github.com/nitishMehrotra/workflow-mapper/actions/workflows/ci.yml/badge.svg)](https://github.com/nitishMehrotra/workflow-mapper/actions/workflows/ci.yml)

> **🔄 Status: Pivoting to WorkflowMapperAgent Foundation** - Building bidirectional human-agent collaboration system for code analysis and spec generation.

A TypeScript monorepo for building the **WorkflowMapperAgent** - an AI system that analyzes codebases, builds workflow graphs, and enables bidirectional synchronization between technical specifications and code implementation.

## 🎯 Vision

This repository serves as the bootstrap environment for developing a **bidirectional human-agent collaboration system** where:
- **Humans write tech specs** → **Agents implement code**
- **Agents analyze existing code** → **Generate tech specs** → **Humans maintain/refine**
- **Living documentation** that stays synchronized with code changes

See [`milestone-experiment-1.md`](../milestone-experiment-1.md) for the complete vision.

## 🚀 Quick Start

### Prerequisites

- Node.js 20.11.0+
- pnpm 8.15.4+
- <PERSON><PERSON> & Docker Compose

### Installation

```bash
# Clone the repository
git clone <repo-url>
cd workflow-mapper

# Install dependencies
pnpm install

# Setup Git Hooks (for commit message validation)
chmod +x scripts/setup-git-hooks.sh
./scripts/setup-git-hooks.sh

# Start development servers
pnpm dev:api    # API server on :3000
pnpm dev:web    # Web app on :5173
```

### Development

```bash
# Run all tests
pnpm test

# Lint and format
pnpm lint

# Type checking
pnpm type-check

# Build all packages
pnpm build
```

### Docker

```bash
# Start full stack (API + Neo4j)
docker compose up

# Health checks
curl http://localhost:3000/health
curl http://localhost:5173/health  # Proxies to API
```

## 📁 Project Structure

```
workflow-mapper/
├── apps/
│   ├── api/          # Express API server
│   └── web/          # React web application
├── packages/
│   └── shared/       # Shared utilities and types
├── docs/
│   └── tech-specs/   # Technical specifications
└── scripts/          # Build and deployment scripts
```

## 🛠 Scripts

- `pnpm dev:api` - Start API development server
- `pnpm dev:web` - Start web development server
- `pnpm build` - Build all packages
- `pnpm test` - Run all tests
- `pnpm lint` - Lint all code
- `pnpm type-check` - TypeScript type checking

## 🔧 Configuration

### Environment Variables

- `NODE_ENV=development` - Enables mock DB mode for local development
- `PORT` - API server port (default: 3000)

### Mock DB Mode

For local development without Neo4j:

```bash
NODE_ENV=development pnpm dev:api
```

## 📚 Documentation

- [Contributing Guidelines](./CONTRIBUTING.md)
- [Security Policy](./SECURITY.md)
- [Changelog](./CHANGELOG.md)
- [Technical Specifications](./docs/tech-specs/)

## 🤝 Contributing

Please read [CONTRIBUTING.md](./CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.
# Test comment
