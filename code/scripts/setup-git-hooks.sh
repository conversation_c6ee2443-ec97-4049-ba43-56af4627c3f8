#!/bin/sh

# Script to set up Git hooks
echo "Setting up Git hooks..."

# Ensure Git uses standard hooks directory (not <PERSON>sky)
cd ..
git config --unset core.hookspath 2>/dev/null || true
cd code

# Create hooks directory if it doesn't exist
mkdir -p ../.git/hooks

# Create hooks directory for storing hooks
mkdir -p ../.github/hooks

# Copy commit-msg hook if it doesn't exist in .github/hooks
if [ ! -f ../.github/hooks/commit-msg ]; then
    cp ../.github/hooks/commit-msg ../.github/hooks/
fi

# Create pre-commit hook for lint checking
cat > ../.github/hooks/pre-commit << 'EOF'
#!/bin/sh
#
# Git pre-commit hook to run lint check on entire repository
# Ensures code quality standards before commits
#

echo "🔍 Running lint check on entire repository..."

# Change to the code directory where package.json is located
cd code

# Run lint check on entire codebase
if ! pnpm lint; then
    echo ""
    echo "❌ Lint check failed!"
    echo "🔧 Please fix the linting errors above before committing."
    echo "💡 You can run 'pnpm lint --fix' to automatically fix some issues."
    echo "🚫 To bypass this check (not recommended), use: git commit --no-verify"
    echo ""
    exit 1
fi

echo "✅ Lint check passed! Code quality looks good."
exit 0
EOF

# Copy hooks to git hooks directory
cp ../.github/hooks/commit-msg ../.git/hooks/
cp ../.github/hooks/pre-commit ../.git/hooks/

# Make hooks executable
chmod +x ../.git/hooks/commit-msg
chmod +x ../.git/hooks/pre-commit

echo "✅ Git hooks setup complete!"
echo "📋 Configured hooks:"
echo "   • pre-commit: Runs lint check on entire repository"
echo "   • commit-msg: Validates semantic versioning format"
echo ""
echo "💡 To bypass hooks (not recommended): git commit --no-verify"
